"""Modular configuration builder for interactive Telegram menu system."""

import time
from typing import Dict, Any, Optional

from src.logger import get_logger
from src.bot.telegram.telegram import TelegramManager
from src.bot.telegram._build.strategy_builder import StrategyBuilder

LOGGER = get_logger(__name__)


class GeneralConfigBuilder:
    """
    Builder for general configuration parameters via interactive Telegram prompts.
    Handles years_ago, investment_amount, and investment_kind configuration.
    """

    def __init__(self, telegram_manager: TelegramManager, timeout_seconds: int = 300):
        """
        Initialize the general configuration builder.

        Args:
            telegram_manager (TelegramManager): Telegram manager instance.
            timeout_seconds (int): Seconds to wait for each user response.
        """
        LOGGER.activate()
        self.telegram_manager = telegram_manager
        self.timeout_seconds = timeout_seconds
        LOGGER.info("🔧 General configuration builder initialized")

    def build_general_config_interactive(self) -> Optional[Dict[str, Any]]:
        """
        Build general configuration interactively through Telegram prompts.

        Returns:
            Optional[Dict[str, Any]]: General configuration dict or None if cancelled.
        """
        try:
            self.telegram_manager.send_message(
                "🔧 **General Setup**\n\n"
                "Let's configure your investment parameters!"
            )

            # Years ago
            years_ago = self._ask_years_ago()
            if years_ago is None:
                return None

            # Investment amount
            investment_amount = self._ask_investment_amount()
            if investment_amount is None:
                return None

            # Investment frequency
            investment_kind = self._ask_investment_kind()
            if investment_kind is None:
                return None

            general_config = {
                "years_ago": years_ago,
                "investment_amount": investment_amount,
                "investment_kind": investment_kind,
            }

            self.telegram_manager.send_message(
                "✅ **General Setup Complete!**\n\n"
                f"• Years of data: {years_ago}\n"
                f"• Investment amount: ${investment_amount:,}\n"
                f"• Frequency: {investment_kind.title()}"
            )

            return general_config

        except Exception as e:
            LOGGER.error(f"❌ Error building general config: {e}")
            return None

    def _ask_years_ago(self) -> Optional[int]:
        """Ask about years of historical data with free-form input."""
        keyboard = {
            "inline_keyboard": [
                [{"text": "� Use Default (10)", "callback_data": "years_default"}],
            ]
        }

        self.telegram_manager.send_message(
            "📅 **Years of Historical Data**\n\n"
            "How many years back should we analyze?\n\n"
            "**Examples:** 5, 10, 15, 20, 25\n"
            "**Range:** 1-50 years\n"
            "**Default: 10 years**\n\n"
            "� **Type a number** or click default:",
            reply_markup=keyboard,
        )

        response = self._wait_for_response_with_callbacks()
        if response is None or response == "years_default":
            self.telegram_manager.send_message("✅ Using default: 10 years")
            return 10

        # Try to parse as number
        try:
            years = int(float(response))  # Allow decimal input but convert to int
            if 1 <= years <= 50:
                self.telegram_manager.send_message(f"✅ Selected: {years} years")
                return years
            else:
                self.telegram_manager.send_message(
                    f"⚠️ Invalid range. Using default: 10 years\n"
                    f"(Valid range: 1-50 years)"
                )
                return 10
        except (ValueError, TypeError):
            self.telegram_manager.send_message(
                f"⚠️ Invalid input '{response}'. Using default: 10 years\n"
                f"Please enter a number between 1-50."
            )
            return 10

    def _ask_investment_amount(self) -> Optional[float]:
        """Ask about investment amount with free-form input."""
        keyboard = {
            "inline_keyboard": [
                [{"text": "� Use Default ($1,000)", "callback_data": "amount_default"}],
            ]
        }

        self.telegram_manager.send_message(
            "💰 **Investment Amount per Period**\n\n"
            "How much do you want to invest each period?\n\n"
            "**Examples:** 500, 1000, 1240.549, 2500\n"
            "**Range:** $0.01 - $1,000,000\n"
            "**Default: $1,000**\n\n"
            "� **Type an amount** (without $ symbol) or click default:",
            reply_markup=keyboard,
        )

        response = self._wait_for_response_with_callbacks()
        if response is None or response == "amount_default":
            self.telegram_manager.send_message("✅ Using default: $1,000")
            return 1000.0

        # Try to parse as number
        try:
            # Remove any currency symbols or commas that user might include
            cleaned_response = response.replace("$", "").replace(",", "").strip()
            amount = float(cleaned_response)

            if 0.01 <= amount <= 1000000:
                # Format display based on whether it's a whole number or has decimals
                if amount == int(amount):
                    display_amount = f"${int(amount):,}"
                else:
                    display_amount = f"${amount:,.2f}"

                self.telegram_manager.send_message(f"✅ Selected: {display_amount}")
                return amount
            else:
                self.telegram_manager.send_message(
                    f"⚠️ Invalid range. Using default: $1,000\n"
                    f"(Valid range: $0.01 - $1,000,000)"
                )
                return 1000.0
        except (ValueError, TypeError):
            self.telegram_manager.send_message(
                f"⚠️ Invalid input '{response}'. Using default: $1,000\n"
                f"Please enter a valid number (e.g., 1000)."
            )
            return 1000.0

    def _ask_investment_kind(self) -> Optional[str]:
        """Ask about investment frequency."""
        keyboard = {
            "inline_keyboard": [
                [
                    {"text": "📅 Daily", "callback_data": "kind_daily"},
                    {"text": "📊 Weekly", "callback_data": "kind_weekly"},
                ],
                [
                    {"text": "📈 Monthly", "callback_data": "kind_monthly"},
                    {"text": "📋 Quarterly", "callback_data": "kind_quarterly"},
                ],
                [
                    {"text": "📆 Yearly", "callback_data": "kind_yearly"},
                    {"text": "🔧 Default (Monthly)", "callback_data": "kind_default"},
                ],
            ]
        }

        self.telegram_manager.send_message(
            "📊 **Investment Frequency**\n\n"
            "How often do you want to invest?\n\n"
            "• **Daily** - Invest every day\n"
            "• **Weekly** - Invest every week\n"
            "• **Monthly** - Invest every month\n"
            "• **Quarterly** - Invest every quarter\n"
            "• **Yearly** - Invest every year\n\n"
            "**Default: Monthly**\n\n"
            "👇 **Click your choice:**",
            reply_markup=keyboard,
        )

        response = self._wait_for_response_with_callbacks()
        if response is None or response == "kind_default":
            self.telegram_manager.send_message("✅ Using default: Monthly")
            return "monthly"

        kind_map = {
            "kind_daily": "daily",
            "kind_weekly": "weekly",
            "kind_monthly": "monthly",
            "kind_quarterly": "quarterly",
            "kind_yearly": "yearly",
        }

        kind = kind_map.get(response, "monthly")
        self.telegram_manager.send_message(f"✅ Selected: {kind.title()}")
        return kind

    def _wait_for_response_with_callbacks(self) -> Optional[str]:
        """Wait for user response with callback support."""
        start_time = time.time()
        while time.time() - start_time < self.timeout_seconds:
            updates = self.telegram_manager._get_updates()
            for update in updates:
                if "callback_query" in update:
                    callback_data = update["callback_query"]["data"]
                    callback_id = update["callback_query"]["id"]

                    # Acknowledge the callback
                    self.telegram_manager.answer_callback_query(callback_id, "Selection received")
                    return callback_data
                elif "message" in update and "text" in update["message"]:
                    # Return original text (not lowercased) to preserve numeric input
                    return update["message"]["text"].strip()

            time.sleep(1)

        return None


class ConfigBuilder:
    """
    Modular builder for creating complete configurations via interactive Telegram prompts.
    """

    def __init__(
        self,
        telegram_manager: TelegramManager,
        timeout_seconds: int = 300,
        strategy_builder: Optional[StrategyBuilder] = None,
    ):
        """
        Initialize the configuration builder.

        Args:
            telegram_manager (TelegramManager): Telegram manager instance.
            timeout_seconds (int): Seconds to wait for each user response.
            strategy_builder (StrategyBuilder, optional): Existing strategy builder to reuse.
        """
        LOGGER.activate()
        self.telegram_manager = telegram_manager
        self.timeout_seconds = timeout_seconds

        # Use provided strategy builder or create a new one
        if strategy_builder:
            self.strategy_builder = strategy_builder
        else:
            self.strategy_builder = StrategyBuilder(telegram_manager, timeout_seconds)

        LOGGER.info("🎨 Configuration builder initialized")

    def build_config_interactive(self, base_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Build configuration interactively through Telegram prompts.

        Args:
            base_config (Dict[str, Any]): Base configuration for fallback.

        Returns:
            Dict[str, Any]: Complete custom configuration.
        """
        LOGGER.info("🎨 Starting interactive configuration building")

        self.telegram_manager.send_message(
            "🎨 **Configuration Builder**\n\n"
            "Let's build your custom configuration!\n\n"
            "Choose what you want to configure:\n\n"
            "**1. General** - Investment parameters (years, amount, frequency, etc.)\n"
            "**2. Strategies** - Investment strategies only\n\n"
            "You can choose both by typing '1 2' or '12'\n\n"
            "**Default: Strategies only (2)**\n\n"
            "💡 Reply 'd' to use default"
        )

        # Get user choice for what to configure
        config_choice = self._ask_config_choice()

        result_config = dict(base_config)

        # Build general config if requested
        if "general" in config_choice:
            LOGGER.info("🔧 Building general configuration")
            general_config = self._build_general_config_interactive()
            if general_config:
                result_config["general"] = general_config

        # Build strategies if requested
        if "strategies" in config_choice:
            LOGGER.info("📋 Building strategies configuration")
            strategies = self.strategy_builder.build_strategies_interactive()
            if strategies:
                result_config["strategies"] = strategies

        if not strategies and "strategies" in config_choice:
            LOGGER.warning("⚠️ No strategies created, using base config")
            return base_config

        LOGGER.info("✅ Configuration building completed")
        return result_config

    def _ask_config_choice(self) -> set:
        """Ask user what parts of config to customize."""
        response = self._wait_for_response()

        if response is None or response.strip().lower() == "d":
            self.telegram_manager.send_message("Using default: Strategies only")
            return {"strategies"}

        response_clean = response.strip().lower().replace(" ", "")

        choices = set()
        if "1" in response_clean or "general" in response_clean:
            choices.add("general")
        if (
            "2" in response_clean
            or "strategies" in response_clean
            or "strategy" in response_clean
        ):
            choices.add("strategies")

        if not choices:
            self.telegram_manager.send_message(
                "⚠️ Invalid choice, using default: Strategies only"
            )
            return {"strategies"}

        choice_text = []
        if "general" in choices:
            choice_text.append("General settings")
        if "strategies" in choices:
            choice_text.append("Strategies")

        self.telegram_manager.send_message(f"Configuring: {' and '.join(choice_text)}")
        return choices

    def _build_general_config_interactive(self) -> Optional[Dict[str, Any]]:
        """Build general configuration interactively."""
        try:
            self.telegram_manager.send_message(
                "🔧 **General Configuration**\n\n"
                "Let's set up your investment parameters!"
            )

            # Years ago
            years_ago = self._ask_numeric_question(
                "📅 **Years of Historical Data**\n\n"
                "How many years back should we analyze?\n\n"
                "**Examples:** 5, 10, 15, 20\n"
                "**Default: 20**\n\n"
                "💡 Reply 'd' to use default",
                default_value=20,
                min_value=1,
                max_value=50,
            )

            # Investment amount
            investment_amount = self._ask_numeric_question(
                "💰 **Investment Amount per Period**\n\n"
                "How much do you want to invest each period?\n\n"
                "**Examples:** 500, 1000, 2000\n"
                "**Default: 1000**\n\n"
                "💡 Reply 'd' to use default",
                default_value=1000,
                min_value=1,
                max_value=1000000,
            )

            # Investment frequency
            investment_kind = self._ask_investment_frequency()

            # Animation duration
            animation_duration = self._ask_numeric_question(
                "🎬 **Animation Duration**\n\n"
                "How long should the animation be (in seconds)?\n\n"
                "**Examples:** 10, 15, 20, 30\n"
                "**Default: 20**\n\n"
                "💡 Reply 'd' to use default",
                default_value=20,
                min_value=5,
                max_value=120,
            )

            general_config = {
                "years_ago": years_ago,
                "investment_amount": investment_amount,
                "investment_kind": investment_kind,
                "animation_duration_seconds": animation_duration,
                "fps": 30,  # Keep default
                "tax_rate": 0.26375,  # Keep default
                "tax_free_return_threshold_per_annu": 1000,  # Keep default
                "save_data": False,  # Keep default
            }

            self.telegram_manager.send_message(
                "✅ **General Configuration Created!**\n\n"
                f"• Years of data: {years_ago}\n"
                f"• Investment amount: ${investment_amount:,}\n"
                f"• Frequency: {investment_kind.title()}\n"
                f"• Animation duration: {animation_duration}s"
            )

            return general_config

        except Exception as e:
            LOGGER.error(f"❌ Error building general config: {e}")
            return None

    def _ask_investment_frequency(self) -> str:
        """Ask about investment frequency."""
        self.telegram_manager.send_message(
            "📊 **Investment Frequency**\n\n"
            "How often do you want to invest?\n\n"
            "**1. Daily**\n"
            "**2. Weekly**\n"
            "**3. Monthly**\n"
            "**4. Quarterly**\n"
            "**5. Yearly**\n\n"
            "**Default: Monthly (3)**\n\n"
            "💡 Reply 'd' to use default"
        )

        response = self._wait_for_response()
        if response is None or response.strip().lower() == "d":
            self.telegram_manager.send_message("Using default: Monthly")
            return "monthly"

        response_stripped = response.strip().lower()

        frequency_map = {
            "1": "daily",
            "daily": "daily",
            "d": "daily",
            "2": "weekly",
            "weekly": "weekly",
            "w": "weekly",
            "3": "monthly",
            "monthly": "monthly",
            "m": "monthly",
            "4": "quarterly",
            "quarterly": "quarterly",
            "q": "quarterly",
            "5": "yearly",
            "yearly": "yearly",
            "y": "yearly",
        }

        frequency = frequency_map.get(response_stripped, "monthly")
        self.telegram_manager.send_message(f"Selected: {frequency.title()}")
        return frequency

    def _ask_numeric_question(
        self,
        question: str,
        default_value: int,
        min_value: int = None,
        max_value: int = None,
    ) -> int:
        """Ask a numeric question with validation."""
        self.telegram_manager.send_message(question)

        response = self._wait_for_response()
        if response is None or response.strip().lower() == "d":
            self.telegram_manager.send_message(f"Using default: {default_value}")
            return default_value

        try:
            value = int(response.strip())
            if min_value is not None and value < min_value:
                self.telegram_manager.send_message(
                    f"⚠️ Value too low, using minimum: {min_value}"
                )
                return min_value
            if max_value is not None and value > max_value:
                self.telegram_manager.send_message(
                    f"⚠️ Value too high, using maximum: {max_value}"
                )
                return max_value
            return value
        except ValueError:
            self.telegram_manager.send_message(
                f"⚠️ Invalid number, using default: {default_value}"
            )
            return default_value

    def _wait_for_response(self) -> Optional[str]:
        """Wait for user response with timeout."""
        start_time = time.time()

        while (time.time() - start_time) < self.timeout_seconds:
            updates = self.telegram_manager._get_updates()

            for update in updates:
                if "message" in update and "text" in update["message"]:
                    return update["message"]["text"]

            time.sleep(1)

        return None
