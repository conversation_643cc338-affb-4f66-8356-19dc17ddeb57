"""Music manager for handling background music in animations."""

import subprocess
from pathlib import Path
from typing import Op<PERSON>, Dict
import toml
from dataclasses import dataclass
from src.logger import get_logger

LOGGER = get_logger(__name__)


@dataclass
class MusicConfig:
    """Configuration for a music track.

    Attributes:
        filename (str): Name of the music file.
        creator (str): Attribution text for the music creator.
        start_time (float): Start time in seconds where the music should begin playing.
        file_path (Path): Full path to the music file.
    """

    filename: str
    creator: str
    start_time: float
    file_path: Path


class MusicManager:
    """Manages background music integration for animations.

    This class handles loading music configuration, validating music files,
    and integrating audio with video files using FFmpeg.
    """

    @staticmethod
    def _build_music_dir() -> Path:
        """Build the music directory path.

        Returns:
            Path: The music directory path.
        """
        project_root = Path(__file__).parent.parent.parent
        return project_root / "assets" / "music"

    @staticmethod
    def _load_creator_toml_file(music_file: str) -> Dict[str, str]:
        """Load the creators.toml file.

        Returns:
            Dict[str, str]: The path to the creators.toml file.
        """
        creators_toml_file = MusicManager._build_music_dir() / "creators.toml"
        # Load the TOML file
        if not creators_toml_file.exists():
            return False

        return toml.load(creators_toml_file).get(music_file, False)

    def __init__(self, music_dir: Optional[Path] = None):
        """Initialize the MusicManager.

        Args:
            music_dir (Optional[Path]): Path to the music directory.
                Defaults to 'assets/music' relative to project root.
        """

        LOGGER.activate()
        if music_dir is None:
            music_dir = self._build_music_dir()

        self.music_dir = Path(music_dir)
        self.config_file = self.music_dir / "creators.toml"
        self.music_configs: Dict[str, MusicConfig] = {}

        if self.music_dir.exists():
            self._load_music_configs()

    def _load_music_configs(self) -> None:
        """Load music configurations from the creators.toml file."""
        if not self.config_file.exists():
            LOGGER.warning(f"Music config file not found: {self.config_file}")
            return

        try:
            config_data = toml.load(self.config_file)

            for filename, config in config_data.items():
                file_path = self.music_dir / filename

                if not file_path.exists():
                    LOGGER.warning(f"Music file not found: {file_path}")
                    continue

                music_config = MusicConfig(
                    filename=filename,
                    creator=config.get("creator", "Unknown"),
                    start_time=float(config.get("start", 0)),
                    file_path=file_path,
                )

                self.music_configs[filename] = music_config
                LOGGER.info(f"Loaded music config for: {filename}")

        except Exception as e:
            LOGGER.error(f"Error loading music configs: {e}")

    def has_music(self) -> bool:
        """Check if any music files are available.

        Returns:
            bool: True if music files are available, False otherwise.
        """
        return len(self.music_configs) > 0

    def get_available_music(self) -> Dict[str, MusicConfig]:
        """Get all available music configurations.

        Returns:
            Dict[str, MusicConfig]: Dictionary of available music configurations.
        """
        return self.music_configs.copy()

    def select_music(self, filename: Optional[str] = None) -> Optional[MusicConfig]:
        """Select a music track for the animation.

        Args:
            filename (Optional[str]): Specific filename to select. If None,
                selects the first available music track.

        Returns:
            Optional[MusicConfig]: Selected music configuration, or None if no music available.
        """
        if not self.has_music():
            return None

        # If a specific filename is requested, try to use it
        if filename:
            if filename in self.music_configs:
                LOGGER.info(f"Using requested music: {filename}")
                return self.music_configs[filename]
            else:
                LOGGER.warning(
                    f"Requested music '{filename}' not found, using first available"
                )

        # Return the first available music track
        selected = next(iter(self.music_configs.values()))
        LOGGER.info(f"Using default music: {selected.filename}")
        return selected

    def add_music_to_video(
        self,
        video_path: Path,
        output_path: Path,
        music_config: Optional[MusicConfig] = None,
        video_duration: Optional[float] = None,
    ) -> bool:
        """Add background music to a video file.

        Args:
            video_path (Path): Path to the input video file.
            output_path (Path): Path for the output video with music.
            music_config (Optional[MusicConfig]): Music configuration to use.
                If None, selects the first available music.
            video_duration (Optional[float]): Duration of the video in seconds.
                If None, FFmpeg will determine the duration automatically.

        Returns:
            bool: True if music was successfully added, False otherwise.
        """
        if not video_path.exists():
            LOGGER.error(f"Video file not found: {video_path}")
            return False

        if music_config is None:
            return
            # music_config = self.select_music()

        if music_config is None:
            LOGGER.info("No music available, skipping audio integration")
            return False

        try:
            LOGGER.info(f"Adding music to video: {music_config.filename}")

            # Try using subprocess directly for better error handling
            cmd = [
                "ffmpeg",
                "-i",
                str(video_path),
                "-ss",
                str(music_config.start_time),
                "-i",
                str(music_config.file_path),
                "-c:v",
                "copy",
                "-c:a",
                "aac",
                "-shortest",
                "-y",  # Overwrite output file
                str(output_path),
            ]

            LOGGER.debug(f"Running FFmpeg command: {' '.join(cmd)}")

            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=60  # 60 second timeout
            )

            if result.returncode == 0:
                LOGGER.info(f"Successfully created video with music: {output_path}")
                return True
            else:
                LOGGER.error(f"FFmpeg failed with return code {result.returncode}")
                LOGGER.error(f"FFmpeg stderr: {result.stderr}")
                if result.stdout:
                    LOGGER.debug(f"FFmpeg stdout: {result.stdout}")
                return False

        except subprocess.TimeoutExpired:
            LOGGER.error("FFmpeg command timed out")
            return False
        except Exception as e:
            LOGGER.error(f"Error adding music to video: {e}")
            return False

    def get_music_attribution(
        self, music_config: Optional[MusicConfig] = None
    ) -> Optional[str]:
        """Get attribution text for the selected music.

        Args:
            music_config (Optional[MusicConfig]): Music configuration to get attribution for.
                If None, uses the first available music.

        Returns:
            Optional[str]: Attribution text, or None if no music is selected.
        """
        if music_config is None:
            music_config = self.select_music()

        if music_config is None:
            return None

        return music_config.creator
