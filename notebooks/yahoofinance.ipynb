{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c4f9d7b5", "metadata": {}, "outputs": [], "source": ["import yfinance as yf\n", "\n", "ticker = yf.Ticker(\"GC=F\")\n", "ticker = yf.Ticker(\"SI=F\")\n", "ticker = yf.Ticker(\"QQQ\")\n", "\n", "\n", "data = ticker.history(start=\"2010-01-01\", end=\"2025-01-01\", interval=\"1d\")"]}, {"cell_type": "code", "execution_count": 2, "id": "36ffed06", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Open</th>\n", "      <th>High</th>\n", "      <th>Low</th>\n", "      <th>Close</th>\n", "      <th>Volume</th>\n", "      <th>Dividends</th>\n", "      <th>Stock Splits</th>\n", "      <th>Capital Gains</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Date</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2010-01-04 00:00:00-05:00</th>\n", "      <td>40.361984</td>\n", "      <td>40.501374</td>\n", "      <td>40.309712</td>\n", "      <td>40.440388</td>\n", "      <td>62822800</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-01-05 00:00:00-05:00</th>\n", "      <td>40.414253</td>\n", "      <td>40.510084</td>\n", "      <td>40.213881</td>\n", "      <td>40.440388</td>\n", "      <td>62935600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-01-06 00:00:00-05:00</th>\n", "      <td>40.422967</td>\n", "      <td>40.553642</td>\n", "      <td>40.135474</td>\n", "      <td>40.196457</td>\n", "      <td>96033000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-01-07 00:00:00-05:00</th>\n", "      <td>40.257443</td>\n", "      <td>40.309716</td>\n", "      <td>40.004799</td>\n", "      <td>40.222595</td>\n", "      <td>77094100</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2010-01-08 00:00:00-05:00</th>\n", "      <td>40.135478</td>\n", "      <td>40.553646</td>\n", "      <td>40.013513</td>\n", "      <td>40.553646</td>\n", "      <td>88886600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-24 00:00:00-05:00</th>\n", "      <td>523.460590</td>\n", "      <td>528.666941</td>\n", "      <td>522.822246</td>\n", "      <td>528.577209</td>\n", "      <td>17558200</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-26 00:00:00-05:00</th>\n", "      <td>526.941511</td>\n", "      <td>529.853875</td>\n", "      <td>524.936746</td>\n", "      <td>528.218140</td>\n", "      <td>19090500</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-27 00:00:00-05:00</th>\n", "      <td>524.637544</td>\n", "      <td>525.076398</td>\n", "      <td>516.508784</td>\n", "      <td>521.196533</td>\n", "      <td>33839600</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-30 00:00:00-05:00</th>\n", "      <td>514.164934</td>\n", "      <td>518.004864</td>\n", "      <td>510.494512</td>\n", "      <td>514.264648</td>\n", "      <td>34584000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-12-31 00:00:00-05:00</th>\n", "      <td>515.551337</td>\n", "      <td>516.309303</td>\n", "      <td>508.928648</td>\n", "      <td>509.896118</td>\n", "      <td>29117000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3774 rows × 8 columns</p>\n", "</div>"], "text/plain": ["                                 Open        High         Low       Close  \\\n", "Date                                                                        \n", "2010-01-04 00:00:00-05:00   40.361984   40.501374   40.309712   40.440388   \n", "2010-01-05 00:00:00-05:00   40.414253   40.510084   40.213881   40.440388   \n", "2010-01-06 00:00:00-05:00   40.422967   40.553642   40.135474   40.196457   \n", "2010-01-07 00:00:00-05:00   40.257443   40.309716   40.004799   40.222595   \n", "2010-01-08 00:00:00-05:00   40.135478   40.553646   40.013513   40.553646   \n", "...                               ...         ...         ...         ...   \n", "2024-12-24 00:00:00-05:00  523.460590  528.666941  522.822246  528.577209   \n", "2024-12-26 00:00:00-05:00  526.941511  529.853875  524.936746  528.218140   \n", "2024-12-27 00:00:00-05:00  524.637544  525.076398  516.508784  521.196533   \n", "2024-12-30 00:00:00-05:00  514.164934  518.004864  510.494512  514.264648   \n", "2024-12-31 00:00:00-05:00  515.551337  516.309303  508.928648  509.896118   \n", "\n", "                             Volume  Dividends  Stock Splits  Capital Gains  \n", "Date                                                                         \n", "2010-01-04 00:00:00-05:00  62822800        0.0           0.0            0.0  \n", "2010-01-05 00:00:00-05:00  62935600        0.0           0.0            0.0  \n", "2010-01-06 00:00:00-05:00  96033000        0.0           0.0            0.0  \n", "2010-01-07 00:00:00-05:00  77094100        0.0           0.0            0.0  \n", "2010-01-08 00:00:00-05:00  88886600        0.0           0.0            0.0  \n", "...                             ...        ...           ...            ...  \n", "2024-12-24 00:00:00-05:00  17558200        0.0           0.0            0.0  \n", "2024-12-26 00:00:00-05:00  19090500        0.0           0.0            0.0  \n", "2024-12-27 00:00:00-05:00  33839600        0.0           0.0            0.0  \n", "2024-12-30 00:00:00-05:00  34584000        0.0           0.0            0.0  \n", "2024-12-31 00:00:00-05:00  29117000        0.0           0.0            0.0  \n", "\n", "[3774 rows x 8 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": null, "id": "44a96eb2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "stock-reels", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.4"}}, "nbformat": 4, "nbformat_minor": 5}